/// Member API Service - GymKod Pro Mobile
///
/// Bu service Angular frontend'deki member service'inden uyarlanmıştır.
/// Referans: GymProjectFrontend/src/app/services/member.service.ts
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dio/dio.dart';
import '../../../../core/core.dart';
import '../../domain/models/member_models.dart';

/// Member API Service Interface
abstract class MemberApiService {
  /// Kullanıcının QR kodunu al (Angular: getMemberQRByUserId)
  Future<ApiResponse<GetMemberQRByPhoneNumberDto>> getMemberQRByUserId();

  /// Kullanıcının profil bilgilerini al
  Future<ApiResponse<MemberProfileDto>> getMemberProfile();

  /// Kullanıcının profil bilgilerini güncelle
  Future<ApiResponse<void>> updateMemberProfile(MemberProfileUpdateDto profileUpdateDto);
}

/// Member API Service Implementation
class MemberApiServiceImpl implements MemberApiService {
  final ApiService _apiService;

  MemberApiServiceImpl(this._apiService);

  @override
  Future<ApiResponse<GetMemberQRByPhoneNumberDto>> getMemberQRByUserId() async {
    try {
      LoggingService.apiLog('Member QR request', '/member/getmemberqrbyuserid');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/member/getmemberqrbyuserid',
        // JWT token otomatik olarak header'a ekleniyor (interceptor'da)
      );

      if (response.statusCode == 200 && response.data != null) {
        // Backend response format'ını parse et
        final memberQRResponse = MemberQRResponse.fromJson(response.data!);

        LoggingService.apiLog(
          memberQRResponse.success ? 'Member QR success' : 'Member QR failed',
          memberQRResponse.message,
        );

        if (memberQRResponse.success && memberQRResponse.data != null) {
          return ApiResponse.success(
            message: memberQRResponse.message,
            data: memberQRResponse.data!,
          );
        } else {
          return ApiResponse.error(
            message: memberQRResponse.message,
          );
        }
      } else {
        LoggingService.apiLog('Member QR failed', 'Invalid response: ${response.statusCode}');
        return ApiResponse.error(
          message: 'QR kod alınamadı. Lütfen tekrar deneyin.',
        );
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberApiService getMemberQRByUserId');

      // DioException'dan response data'yı çıkarmaya çalış
      if (e is DioException && e.response?.statusCode == 400) {
        try {
          final responseData = e.response?.data;
          if (responseData is Map<String, dynamic> && responseData['message'] != null) {
            LoggingService.apiLog('Member QR failed', 'Bad request with message: ${responseData['message']}');
            return ApiResponse.error(message: responseData['message']);
          }
        } catch (parseError) {
          LoggingService.apiLog('Member QR failed', 'Could not parse 400 error response');
        }
      }

      // Rate limiting kontrolü (Angular frontend pattern)
      if (e.toString().contains('429') || e.toString().contains('Too Many Requests')) {
        return ApiResponse.error(
          message: 'Çok fazla istek gönderildi. 3 dakika sonra tekrar deneyin.',
        );
      }

      // Network error kontrolü
      if (e.toString().contains('SocketException') ||
          e.toString().contains('TimeoutException')) {
        return ApiResponse.error(
          message: 'İnternet bağlantınızı kontrol edin.',
        );
      }

      // Unauthorized error kontrolü
      if (e.toString().contains('401') || e.toString().contains('Unauthorized')) {
        return ApiResponse.error(
          message: 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.',
        );
      }

      // Forbidden error kontrolü (member rolü gerekli)
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        return ApiResponse.error(
          message: 'Bu işlem için yetkiniz bulunmuyor.',
        );
      }

      return ApiResponse.error(
        message: 'QR kod alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<MemberProfileDto>> getMemberProfile() async {
    try {
      LoggingService.authLog('Member profile request');

      final response = await _apiService.get<Map<String, dynamic>>(
        '/member/get-profile',
      );

      if (response.statusCode == 200 && response.data != null) {
        final profileResponse = MemberProfileResponse.fromJson(response.data!);

        if (profileResponse.success && profileResponse.data != null) {
          LoggingService.authLog('Member profile success',
            details: 'User: ${profileResponse.data!.firstName} ${profileResponse.data!.lastName}');

          return ApiResponse.success(
            message: profileResponse.message,
            data: profileResponse.data!,
          );
        } else {
          LoggingService.authLog('Member profile failed', details: profileResponse.message);
          return ApiResponse.error(message: profileResponse.message);
        }
      }

      return ApiResponse.error(
        message: 'Profil bilgileri alınamadı.',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberApiService getMemberProfile');

      // Forbidden error kontrolü (member rolü gerekli)
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        return ApiResponse.error(
          message: 'Bu işlem için yetkiniz bulunmuyor.',
        );
      }

      return ApiResponse.error(
        message: 'Profil bilgileri alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<void>> updateMemberProfile(MemberProfileUpdateDto profileUpdateDto) async {
    try {
      final jsonData = profileUpdateDto.toJson();
      LoggingService.authLog('Member profile update request',
        details: 'User: ${profileUpdateDto.firstName} ${profileUpdateDto.lastName}, JSON: $jsonData');

      final response = await _apiService.post<Map<String, dynamic>>(
        '/member/update-profile',
        data: jsonData,
      );

      if (response.statusCode == 200 && response.data != null) {
        final updateResponse = response.data!;

        if (updateResponse['success'] == true) {
          LoggingService.authLog('Member profile update success');

          return ApiResponse.success(
            message: updateResponse['message'] ?? 'Profil başarıyla güncellendi.',
          );
        } else {
          LoggingService.authLog('Member profile update failed',
            details: updateResponse['message']);
          return ApiResponse.error(
            message: updateResponse['message'] ?? 'Profil güncellenemedi.',
          );
        }
      }

      return ApiResponse.error(
        message: 'Profil güncellenemedi.',
      );
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberApiService updateMemberProfile');

      // Forbidden error kontrolü (member rolü gerekli)
      if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        return ApiResponse.error(
          message: 'Bu işlem için yetkiniz bulunmuyor.',
        );
      }

      return ApiResponse.error(
        message: 'Profil güncellenemedi. Lütfen tekrar deneyin.',
      );
    }
  }
}

/// Member API Service Provider
final memberApiServiceProvider = Provider<MemberApiService>((ref) {
  final apiService = ref.read(apiServiceProvider);
  return MemberApiServiceImpl(apiService);
});

/// Member Repository Interface
abstract class MemberRepository {
  /// Kullanıcının QR kodunu al
  Future<ApiResponse<GetMemberQRByPhoneNumberDto>> getMemberQR();

  /// Kullanıcının profil bilgilerini al
  Future<ApiResponse<MemberProfileDto>> getMemberProfile();

  /// Kullanıcının profil bilgilerini güncelle
  Future<ApiResponse<void>> updateMemberProfile(MemberProfileUpdateDto profileUpdateDto);
}

/// Member Repository Implementation
class MemberRepositoryImpl implements MemberRepository {
  final MemberApiService _memberApiService;

  MemberRepositoryImpl(this._memberApiService);

  @override
  Future<ApiResponse<GetMemberQRByPhoneNumberDto>> getMemberQR() async {
    try {
      LoggingService.authLog('Member QR repository request');

      final result = await _memberApiService.getMemberQRByUserId();

      if (result.isSuccess && result.data != null) {
        LoggingService.authLog('Member QR repository success',
          details: 'Name: ${result.data!.name}, Phone: ${result.data!.phoneNumber}');

        return result;
      } else {
        LoggingService.authLog('Member QR repository failed', details: result.message);
        return result;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberRepository getMemberQR');

      return ApiResponse.error(
        message: 'QR kod alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<MemberProfileDto>> getMemberProfile() async {
    try {
      LoggingService.authLog('Member profile repository request');

      final result = await _memberApiService.getMemberProfile();

      if (result.isSuccess && result.data != null) {
        LoggingService.authLog('Member profile repository success',
          details: 'User: ${result.data!.firstName} ${result.data!.lastName}');

        return result;
      } else {
        LoggingService.authLog('Member profile repository failed', details: result.message);
        return result;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberRepository getMemberProfile');

      return ApiResponse.error(
        message: 'Profil bilgileri alınamadı. Lütfen tekrar deneyin.',
      );
    }
  }

  @override
  Future<ApiResponse<void>> updateMemberProfile(MemberProfileUpdateDto profileUpdateDto) async {
    try {
      LoggingService.authLog('Member profile update repository request',
        details: 'User: ${profileUpdateDto.firstName} ${profileUpdateDto.lastName}');

      final result = await _memberApiService.updateMemberProfile(profileUpdateDto);

      if (result.isSuccess) {
        LoggingService.authLog('Member profile update repository success');
        return result;
      } else {
        LoggingService.authLog('Member profile update repository failed', details: result.message);
        return result;
      }
    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'MemberRepository updateMemberProfile');

      return ApiResponse.error(
        message: 'Profil güncellenemedi. Lütfen tekrar deneyin.',
      );
    }
  }
}

/// Member Repository Provider
final memberRepositoryProvider = Provider<MemberRepository>((ref) {
  final memberApiService = ref.read(memberApiServiceProvider);
  return MemberRepositoryImpl(memberApiService);
});
